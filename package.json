{"name": "wordpress-ai-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@assistant-ui/react": "^0.10.6", "@assistant-ui/react-ai-sdk": "^0.10.6", "@assistant-ui/react-markdown": "^0.10.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.3", "@types/uuid": "^10.0.0", "ai": "^4.3.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.4", "lucide-react": "^0.503.0", "namecheap-api": "^1.0.5", "next": "14.1.0", "node-namecheap": "^0.0.1", "openai": "^4.97.0", "react": "18.2.0", "react-dom": "18.2.0", "react-tooltip": "^5.29.1", "remark-gfm": "^4.0.1", "stripe": "^18.3.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "19.1.2", "autoprefixer": "^10.4.14", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.24", "tailwindcss": "^3.4.17"}}