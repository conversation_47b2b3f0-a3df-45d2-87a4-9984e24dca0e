'use client'
import React from 'react'
import { useSearchParams } from 'next/navigation'

const DomainSetupPage: React.FC = () => {
  const searchParams = useSearchParams()
  const siteId = searchParams.get('siteId')
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 p-8">
      <div className="max-w-lg w-full bg-white p-6 rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-semibold mb-4">Domain Setup</h1>
        {siteId ? (
          <p className="text-gray-700">
            We will guide you through setting up a new domain for site <span className="font-medium">{siteId}</span>.
          </p>
        ) : (
          <p className="text-gray-700">We will guide you through setting up a new domain.</p>
        )}
        <p className="mt-4 text-sm text-gray-500">(This page is under construction)</p>
      </div>
    </div>
  )
}

export default DomainSetupPage
