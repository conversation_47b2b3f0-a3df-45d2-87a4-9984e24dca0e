'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Download, Upload, Save, RefreshCw, ExternalLink, AlertCircle, CheckCircle, Loader2, AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import sampleContent from '@/data/sample-content.json';

interface WordPressPage {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    raw: string;
  };
  status: string;
  type: string;
  slug: string;
  link: string;
  date: string;
  modified: string;
  _warning?: string;
}

interface ContentTemplate {
  title: string;
  content: string;
}

export default function ContentEditorPage() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<WordPressPage | null>(null);
  const [editedTitle, setEditedTitle] = useState('');
  const [editedContent, setEditedContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error' | 'warning'; message: string } | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [authWarning, setAuthWarning] = useState<string | null>(null);

  // Fetch current home page content
  const fetchHomePageContent = async () => {
    setIsLoading(true);
    setAuthWarning(null);
    try {
      const response = await fetch('/api/wordpress-content?action=fetch-home');
      if (response.ok) {
        const pageData: WordPressPage = await response.json();
        setCurrentPage(pageData);
        setEditedTitle(pageData.title.rendered);
        setEditedContent(pageData.content.raw || pageData.content.rendered);
        
        // Check for authentication warning
        if (pageData._warning) {
          setAuthWarning(pageData._warning);
          setNotification({ type: 'warning', message: 'Content loaded in read-only mode. Authentication required for updates.' });
        } else {
          setNotification({ type: 'success', message: 'Home page content fetched successfully!' });
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch home page content');
      }
    } catch (error) {
      setNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to fetch home page content' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update home page content
  const updateHomePageContent = async () => {
    if (!currentPage) return;

    // Check if we have authentication warning
    if (authWarning) {
      setNotification({ 
        type: 'error', 
        message: 'Cannot update content without authentication. Please enable Basic Authentication on your WordPress site.' 
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch(`/api/wordpress-content?action=update&pageId=${currentPage.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: editedTitle,
          content: editedContent,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setNotification({ type: 'success', message: 'Home page updated successfully!' });
        // Refresh the current page data
        await fetchHomePageContent();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update home page');
      }
    } catch (error) {
      setNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to update home page' 
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Load sample content template
  const loadSampleContent = (templateKey: keyof typeof sampleContent) => {
    const template = sampleContent[templateKey] as ContentTemplate;
    setEditedTitle(template.title);
    setEditedContent(template.content);
    setNotification({ type: 'success', message: `Loaded ${templateKey} template!` });
  };

  // Open WordPress site in new tab
  const openWordPressSite = () => {
    window.open('https://new-hestia-project3.instawp.xyz/', '_blank');
  };

  // Clear notification after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  // Fetch content on component mount
  useEffect(() => {
    fetchHomePageContent();
  }, []);

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.push('/')}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">WordPress Content Editor</h1>
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={openWordPressSite}
                variant="outline"
                size="sm"
                className="text-gray-600 hover:text-gray-900"
              >
                <ExternalLink size={16} className="mr-2" />
                View Site
              </Button>
              <Button
                onClick={fetchHomePageContent}
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 size={16} className="mr-2 animate-spin" />
                ) : (
                  <RefreshCw size={16} className="mr-2" />
                )}
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Authentication Warning */}
      {authWarning && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                <strong>Authentication Required:</strong> {authWarning}
              </p>
              <div className="mt-2 text-sm text-yellow-700">
                <p>To enable content updates, you need to:</p>
                <ol className="list-decimal list-inside mt-1 space-y-1">
                  <li>Add Basic Authentication to your WordPress site</li>
                  <li>Or use Application Passwords (WordPress 5.6+)</li>
                  <li>Or contact your WordPress administrator</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Content Templates */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Content Templates</h2>
              <div className="space-y-3">
                <Button
                  onClick={() => loadSampleContent('homePageContent')}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Download size={16} className="mr-2" />
                  Professional Home Page
                </Button>
                <Button
                  onClick={() => loadSampleContent('alternativeContent')}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Download size={16} className="mr-2" />
                  Transform Digital Presence
                </Button>
                <Button
                  onClick={() => loadSampleContent('minimalContent')}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Download size={16} className="mr-2" />
                  Simple & Clean
                </Button>
              </div>

              <div className="mt-6 pt-6 border-t">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Current Page Info</h3>
                {currentPage ? (
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Page ID:</span> {currentPage.id}
                    </div>
                    <div>
                      <span className="font-medium">Slug:</span> {currentPage.slug}
                    </div>
                    <div>
                      <span className="font-medium">Status:</span> {currentPage.status}
                    </div>
                    <div>
                      <span className="font-medium">Last Modified:</span>{' '}
                      {new Date(currentPage.modified).toLocaleDateString()}
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No page data loaded</p>
                )}
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border">
              {/* Editor Header */}
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Edit Home Page Content</h2>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => setShowPreview(!showPreview)}
                    variant="outline"
                    size="sm"
                  >
                    {showPreview ? 'Hide Preview' : 'Show Preview'}
                  </Button>
                  <Button
                    onClick={updateHomePageContent}
                    disabled={isSaving || !currentPage || !!authWarning}
                    className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 size={16} className="mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} className="mr-2" />
                        {authWarning ? 'Authentication Required' : 'Save Changes'}
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* Editor Content */}
              <div className="p-6">
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin" />
                      <p className="text-gray-600">Loading home page content...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Title Editor */}
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                        Page Title
                      </label>
                      <input
                        type="text"
                        id="title"
                        value={editedTitle}
                        onChange={(e) => setEditedTitle(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter page title..."
                      />
                    </div>

                    {/* Content Editor */}
                    <div>
                      <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                        Page Content (HTML)
                      </label>
                      <textarea
                        id="content"
                        value={editedContent}
                        onChange={(e) => setEditedContent(e.target.value)}
                        rows={20}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                        placeholder="Enter HTML content..."
                      />
                    </div>

                    {/* Preview */}
                    {showPreview && (
                      <div className="mt-6 p-4 border border-gray-200 rounded-md bg-gray-50">
                        <h3 className="text-sm font-medium text-gray-700 mb-3">Preview</h3>
                        <div className="prose max-w-none">
                          <h1>{editedTitle}</h1>
                          <div dangerouslySetInnerHTML={{ __html: editedContent }} />
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className="fixed z-50 max-w-sm top-4 right-4">
          <div
            className={`p-4 rounded-lg shadow-lg border ${
              notification.type === 'error'
                ? 'bg-red-50 border-red-200 text-red-800'
                : notification.type === 'warning'
                ? 'bg-yellow-50 border-yellow-200 text-yellow-800'
                : 'bg-green-50 border-green-200 text-green-800'
            }`}
          >
            <div className="flex items-center gap-2">
              {notification.type === 'error' ? (
                <AlertCircle size={20} className="text-red-500" />
              ) : notification.type === 'warning' ? (
                <AlertTriangle size={20} className="text-yellow-500" />
              ) : (
                <CheckCircle size={20} className="text-green-500" />
              )}
              <p className="text-sm font-medium">{notification.message}</p>
            </div>
          </div>
        </div>
      )}
    </main>
  );
} 