import { NextRequest, NextResponse } from 'next/server';
import { 
  fetchHomePageContent, 
  fetchHomePageContentPublic,
  updateWordPressPage, 
  fetchAllPages,
  fetchAllPagesPublic,
  WordPressPageUpdate 
} from '@/lib/wordpress-content';
import { 
  DEFAULT_WORDPRESS_CREDENTIALS, 
  validateWordPressCredentials 
} from '@/lib/wordpress-auth';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'fetch-home';
    const pageId = searchParams.get('pageId');
    const usePublic = searchParams.get('public') === 'true';

    // If public access is requested, skip authentication
    if (usePublic) {
      switch (action) {
        case 'fetch-home':
          const homePageResult = await fetchHomePageContentPublic(DEFAULT_WORDPRESS_CREDENTIALS.baseUrl);
          if (homePageResult.success) {
            return NextResponse.json(homePageResult.data);
          } else {
            return NextResponse.json(
              { error: 'Failed to fetch home page', details: homePageResult.error },
              { status: 500 }
            );
          }

        case 'fetch-all':
          const allPagesResult = await fetchAllPagesPublic(DEFAULT_WORDPRESS_CREDENTIALS.baseUrl);
          if (allPagesResult.success) {
            return NextResponse.json(allPagesResult.data);
          } else {
            return NextResponse.json(
              { error: 'Failed to fetch pages', details: allPagesResult.error },
              { status: 500 }
            );
          }

        default:
          return NextResponse.json(
            { error: 'Invalid action specified' },
            { status: 400 }
          );
      }
    }

    // Try authenticated access first
    const authResult = await validateWordPressCredentials(DEFAULT_WORDPRESS_CREDENTIALS);
    
    if (!authResult.success) {
      // If authentication fails, try public access as fallback
      console.log('Authentication failed, trying public access...');
      
      switch (action) {
        case 'fetch-home':
          const homePageResult = await fetchHomePageContentPublic(DEFAULT_WORDPRESS_CREDENTIALS.baseUrl);
          if (homePageResult.success) {
            return NextResponse.json({
              ...homePageResult.data,
              _warning: 'Using public access - updates will not work without authentication'
            });
          } else {
            return NextResponse.json(
              { error: 'Failed to fetch home page', details: homePageResult.error },
              { status: 500 }
            );
          }

        case 'fetch-all':
          const allPagesResult = await fetchAllPagesPublic(DEFAULT_WORDPRESS_CREDENTIALS.baseUrl);
          if (allPagesResult.success) {
            return NextResponse.json(allPagesResult.data);
          } else {
            return NextResponse.json(
              { error: 'Failed to fetch pages', details: allPagesResult.error },
              { status: 500 }
            );
          }

        default:
          return NextResponse.json(
            { error: 'Invalid action specified' },
            { status: 400 }
          );
      }
    }

    // Authentication successful, proceed with authenticated requests
    switch (action) {
      case 'fetch-home':
        const homePageResult = await fetchHomePageContent(DEFAULT_WORDPRESS_CREDENTIALS);
        if (homePageResult.success) {
          return NextResponse.json(homePageResult.data);
        } else {
          return NextResponse.json(
            { error: 'Failed to fetch home page', details: homePageResult.error },
            { status: 500 }
          );
        }

      case 'fetch-all':
        const allPagesResult = await fetchAllPages(DEFAULT_WORDPRESS_CREDENTIALS);
        if (allPagesResult.success) {
          return NextResponse.json(allPagesResult.data);
        } else {
          return NextResponse.json(
            { error: 'Failed to fetch pages', details: allPagesResult.error },
            { status: 500 }
          );
        }

      case 'fetch-page':
        if (!pageId) {
          return NextResponse.json(
            { error: 'Page ID is required' },
            { status: 400 }
          );
        }
        // For now, we'll use the home page fetch as a fallback
        const pageResult = await fetchHomePageContent(DEFAULT_WORDPRESS_CREDENTIALS);
        if (pageResult.success) {
          return NextResponse.json(pageResult.data);
        } else {
          return NextResponse.json(
            { error: 'Failed to fetch page', details: pageResult.error },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('WordPress content API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'update';
    const pageId = searchParams.get('pageId');

    if (!pageId) {
      return NextResponse.json(
        { error: 'Page ID is required' },
        { status: 400 }
      );
    }

    // Validate credentials first
    const authResult = await validateWordPressCredentials(DEFAULT_WORDPRESS_CREDENTIALS);
    if (!authResult.success) {
      return NextResponse.json(
        { 
          error: 'Authentication failed', 
          details: authResult.error,
          solution: 'Please enable Basic Authentication on your WordPress site or use Application Passwords'
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const updates: WordPressPageUpdate = {};

    if (body.title) {
      updates.title = body.title;
    }
    if (body.content) {
      updates.content = body.content;
    }
    if (body.status) {
      updates.status = body.status;
    }

    const updateResult = await updateWordPressPage(
      DEFAULT_WORDPRESS_CREDENTIALS,
      parseInt(pageId),
      updates
    );

    if (updateResult.success) {
      return NextResponse.json({
        success: true,
        data: updateResult.data,
        message: 'Page updated successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to update page', details: updateResult.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('WordPress content update API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 