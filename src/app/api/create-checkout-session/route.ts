// /api/create-checkout-session
import <PERSON><PERSON> from 'stripe'
import { getPriceId } from '@/lib/stripe-plans';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

export async function POST(request: Request) {
  try {
    const { planId, isYearly, siteId } = await request.json()

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      customer: process.env.STRIPE_TEST_CUSTOMER_ID, // Associate with a customer
      line_items: [
        {
          price: getPriceId(planId, isYearly), // Your price IDs
          quantity: 1,
        },
      ],
      metadata: {
        planId,
        isYearly: String(isYearly),
        siteId: siteId || '',
      },
      success_url: `${baseUrl}/dashboard?siteId=${siteId || ''}&postCheckout=1`,
      cancel_url: `${baseUrl}/payments?siteId=${siteId || ''}`,
      
    })

    return Response.json({ url: session.url })
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return new Response(JSON.stringify({ error: 'Failed to create checkout session' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}