import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  const { rawContent, config, type, year } = await req.json();

  if (!rawContent || !config || !type) {
    return NextResponse.json({ error: 'rawContent, config, and type are required' }, { status: 400 });
  }

  let prompt = '';
  if (type === 'header') {
    prompt = `You are an expert HTML content editor for WordPress block themes. Given the following HTML header content, update it according to these rules:\n\n- If the content contains <!-- wp:site-title /-->, replace it with the site_name from the configuration.\n- If there is an <img> tag, replace its src attribute with the logo_url from the configuration.\n- If there is no <img> tag and no <!-- wp:site-title /-->, replace the first heading (h1-h6) content with the site_name from the configuration.\n- Do not add any explanations or extra text. Return only the updated HTML content.\n\nHTML Content:\n${rawContent}\n\nConfiguration:\n${JSON.stringify(config)}\n`;
  } else if (type === 'footer') {
    prompt = `You are an HTML content editor. Modify the following HTML footer content based on the given configuration.\n\nHTML Content:\n${rawContent}\n\nConfiguration:\n- Replace the copyright year and site name (e.g., © 2024 Old Name) with the current year and the site_name from the configuration.\n- Use the year: ${year || new Date().getFullYear()} and site_name: ${config.site_name}.\n\nReturn the updated HTML content only.`;
  } else {
    return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
  }

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });

    const updatedContent = response.choices[0].message?.content || '';

    return NextResponse.json({ updatedContent });
  } catch (error) {
    console.error('Error updating content:', error);
    return NextResponse.json({ error: 'Content update failed' }, { status: 500 });
  }
}
