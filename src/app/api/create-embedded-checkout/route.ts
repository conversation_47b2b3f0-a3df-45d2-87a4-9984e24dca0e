import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';

export async function POST(request: Request) {
    try {
        const { planId, isYearly } = await request.json();

        // Map planIds to Stripe price IDs
        const priceMapping: { [key: string]: { monthly: string; yearly: string } } = {
            starter: {
                monthly: 'price_1Rh6gZHKmibvltr1B4VM6bW3',
                yearly: 'price_1Rh6gZHKmibvltr1UlRdLT7z'
            },
            pro: {
                monthly: 'price_1RiWu5HKmibvltr1OFEmIFmu',
            },
            turbo: {
                monthly: 'price_1RiYP4HKmibvltr1m0dH0fQL',
            }
        };

        const selectedPlan = priceMapping[planId];
        if (!selectedPlan) {
            throw new Error('Invalid plan selected');
        }

        const priceId = isYearly ? selectedPlan.yearly : selectedPlan.monthly;

        const session = await stripe.checkout.sessions.create({
            ui_mode: 'embedded',
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1
                },
            ],
            mode: 'subscription',
            return_url: `${request.headers.get('origin')}/return?session_id={CHECKOUT_SESSION_ID}`,
        });

        return NextResponse.json({ 
            clientSecret: session.client_secret,
            sessionId: session.id
        });
    } catch (error: any) {
        console.error('Checkout session creation error:', error);
        return NextResponse.json(
            { message: error.message || 'Failed to create checkout session' }, 
            { status: 500 }
        );
    }
}
