import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  const { description, currentHtml } = await req.json();

  if (!description || !currentHtml) {
    return NextResponse.json({ error: 'description and currentHtml are required' }, { status: 400 });
  }

  const prompt = `You are an expert WordPress homepage editor. Given the following HTML for a homepage, rewrite all visible text (headings, paragraphs, button labels, etc.) to match this website description: "${description}". Preserve the HTML structure, layout, and style as much as possible. Do not add explanations or comments. Return only the updated HTML.` +
    '\n\nHomepage HTML:\n' + currentHtml;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });
    const updatedHtml = response.choices[0].message?.content || '';
    return NextResponse.json({ updatedHtml });
  } catch (error) {
    console.error('AI homepage generation failed:', error);
    return NextResponse.json({ error: 'AI homepage generation failed' }, { status: 500 });
  }
} 