import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const { domain, price, siteId } = await request.json();
    if (!domain || !price || !siteId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const session = await stripe.checkout.sessions.create({
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'aud',
            product_data: {
              name: `Domain Registration: ${domain}`,
              description: `Register domain ${domain} for 1 year`,
            },
            unit_amount: Math.round(price * 100), // Stripe expects cents
          },
          quantity: 1,
        },
      ],
      metadata: {
        domain,
        siteId,
      },
      success_url: `${baseUrl}/dashboard/domain/complete?session_id={CHECKOUT_SESSION_ID}&domain=${encodeURIComponent(domain)}&siteId=${encodeURIComponent(siteId)}`,
      cancel_url: `${baseUrl}/dashboard/domain`,
    });

    return NextResponse.json({ url: session.url });
  } catch (error: any) {
    console.error('Error creating domain checkout session:', error);
    return NextResponse.json({ error: 'Failed to create domain checkout session' }, { status: 500 });
  }
} 