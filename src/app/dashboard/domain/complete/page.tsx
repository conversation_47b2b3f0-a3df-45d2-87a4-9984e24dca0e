"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from '../../../../components/providers/AuthProvider';

export default function DomainCompletePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const domain = searchParams.get("domain");
  const siteId = searchParams.get("siteId");

  const { user, loading: authLoading } = useAuth();

  const [status, setStatus] = useState("Verifying payment...");
  const [error, setError] = useState<string | null>(null);
  const hasRun = useRef(false);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;
    const processDomainRegistration = async () => {
      if (authLoading || !user) {
        return;
      }
      if (!sessionId || !domain || !siteId) {
        setError("Missing required parameters.");
        return;
      }
      setStatus("Verifying payment...");
      try {
        // 1. Verify payment
        const verifyRes = await fetch("/api/verify-session", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sessionId }),
        });
        const verifyData = await verifyRes.json();
        if (!verifyRes.ok || verifyData.error) {
          throw new Error(verifyData.error || "Payment verification failed.");
        }
        if (
          verifyData.paymentStatus !== "paid" &&
          verifyData.status !== "complete"
        ) {
          throw new Error("Payment not completed.");
        }
        setStatus("Registering your domain...");
        // 2. Register domain
        const registerRes = await fetch("/api/namecheap", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ domain, action: "register", siteId }),
        });
        const registerData = await registerRes.json();
        if (!registerRes.ok || registerData.error) {
          throw new Error(registerData.error || "Domain registration failed.");
        }
        setStatus("Domain registered! Redirecting to dashboard...");
        setTimeout(() => router.replace("/dashboard"), 2000);
      } catch (err: any) {
        setError(err.message || "An error occurred.");
        setStatus("");
      }
    };
    processDomainRegistration();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId, domain, siteId, authLoading, user]);

  if (authLoading || !user) {
    return <div className="py-8 text-center">Checking authentication...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <h1 className="text-xl sm:text-2xl font-bold mb-4 text-gray-800">Domain Registration</h1>
        {status && <p className="mb-4 text-sm sm:text-base text-gray-700">{status}</p>}
        {error && (
          <div className="text-red-600 font-semibold mb-4 text-sm sm:text-base break-words">{error}</div>
        )}
        {!error && (
          <div className="flex items-center justify-center mb-4">
            <svg className="animate-spin h-6 w-6 sm:h-8 sm:w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
          </div>
        )}
        {error && (
          <button
            className="w-full sm:w-auto mt-4 px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            onClick={() => router.replace("/dashboard/domain")}
          >
            Back to Domain Page
          </button>
        )}
      </div>
    </div>
  );
} 