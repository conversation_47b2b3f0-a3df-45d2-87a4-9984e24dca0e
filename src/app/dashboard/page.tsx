'use client'
import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import MapDomainModal from './MapDomainModal';
import AskDomainModal from './AskDomainModal';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../components/providers/AuthProvider';

interface Site {
  id: string;
  site_name: string;
  expiry_status: 'Permanent' | 'Temporary';
  expiry_time?: string; // Add expiry_time as optional
}

const DashboardPage: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user, loading } = useAuth();
  const [sites, setSites] = useState<Site[]>([]);
  const [sitesLoading, setSitesLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState<string>("");
  const [isMapDomainOpen, setIsMapDomainOpen] = useState(false);
  const [isAskDomainOpen, setIsAskDomainOpen] = useState(false);
  const [selectedSiteName, setSelectedSiteName] = useState<string>('');
  const [selectedSiteId, setSelectedSiteId] = useState<string>('');
  const supabase = createClientComponentClient();

  // Helper function to format expiry date
  const formatExpiryDate = (expiryTime: string | undefined): string => {
    if (!expiryTime) return '';
    try {
      const date = new Date(expiryTime);
      const now = new Date();
      const formattedDate = date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
      
      if (date.getTime() <= now.getTime()) {
        return `Expired ${formattedDate}`;
      }
      
      return `Expires ${formattedDate}`;
    } catch {
      return '';
    }
  };

  // Helper function to get tooltip text
  const getTooltipText = (site: Site): string => {
    if (!site.expiry_time) return 'No expiry time set';
    const expiry = new Date(site.expiry_time);
    const now = new Date();
    const diffMs = expiry.getTime() - now.getTime();
    if (diffMs <= 0) return 'Expired';
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    return `${diffDays} days ${diffHours} hours left (expires at ${expiry.toLocaleString()})`;
  };

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    // Trigger AskDomainModal if redirected from checkout
    const postCheckout = searchParams.get('postCheckout');
    const siteIdFromParam = searchParams.get('siteId');
    if (postCheckout && siteIdFromParam) {
      setSelectedSiteId(siteIdFromParam);
      setIsAskDomainOpen(true);
    }
  }, [searchParams]);

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const { data, error } = await supabase
          .from('user-websites')
          .select('id, site_name, expiry_status, expiry_time'); // Fetch expiry_time

        if (error) {
          throw error;
        }

        setSites(data as Site[]);
      } catch (err) {
        console.error('Error fetching sites:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setSitesLoading(false);
      }
    };

    fetchSites();
  }, []);

  if (loading || !user) {
    return <div className="py-8 text-center">Checking authentication...</div>;
  }

  if (sitesLoading) {
    return <div className="py-8 text-center">Loading sites...</div>;
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">Error: {error}</div>;
  }

  return (
    <div className="flex flex-col h-full bg-gray-100">
      {/* Dashboard Header */}
      <div className="flex flex-col p-4 mb-4 space-y-4 bg-white rounded-lg shadow-md sm:flex-row sm:items-center sm:justify-between sm:p-6 sm:mb-6 sm:space-y-0">
        <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Websites</h1>
        <div className="flex items-center space-x-4">
          <div className="relative w-full sm:w-auto">
            <input
              type="text"
              placeholder="Search websites..."
              className="w-full py-2 pl-10 pr-4 text-sm border rounded-md sm:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:text-base"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <svg
              className="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 sm:w-5 sm:h-5 left-3 top-1/2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Sites Table - Desktop */}
      <div className="flex-1 hidden overflow-hidden bg-white rounded-lg shadow-md md:block">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Site Name
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Actions
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Expiry
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {(search.trim() ? sites.filter(site => site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site) => (
              <tr key={site.id}>
                <td className="flex items-center px-4 py-4 text-sm font-medium text-gray-900 lg:px-6 whitespace-nowrap">
                  {site.site_name}
                  <a href="#" className="ml-2 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </td>
                <td className="px-4 py-4 text-sm font-medium text-right lg:px-6 whitespace-nowrap">
                  <div className="flex items-center justify-end space-x-2">
                    {/* Map Domain Icon - Globe icon, always visible */}
                    <button
                      className="p-1 text-blue-500 hover:text-blue-700"
                      title="Map Domain"
                      onClick={() => { setSelectedSiteName(site.site_name); setIsMapDomainOpen(true); }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                    {/* Go Live Icon - Rocket icon, only for Temporary sites */}
                    {site.expiry_status === 'Temporary' && (
                      <button
                        className="p-1 text-green-500 hover:text-green-700"
                        title="Choose Plan"
                        onClick={() => { window.location.href = `/payments?siteId=${site.id}` }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </button>
                    )}
                    {/* Change Plan Icon - Credit card icon, only for Permanent sites */}
                    {site.expiry_status === 'Permanent' && (
                      <button
                        className="p-1 text-purple-500 hover:text-purple-700"
                        title="Change Plan"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <rect x="2" y="7" width="20" height="10" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none" />
                          <path d="M2 11h20" stroke="currentColor" strokeWidth="2" />
                          <circle cx="7" cy="15" r="1" fill="currentColor" />
                          <circle cx="11" cy="15" r="1" fill="currentColor" />
                        </svg>
                      </button>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 text-sm lg:px-6">
                  {/* Enhanced expiry display for desktop */}
                  {site.expiry_status === 'Temporary' ? (
                    <div className="flex flex-col space-y-1">
                      <span
                        className="inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full w-fit"
                        title={getTooltipText(site)}
                      >
                        Temporary
                      </span>
                      {site.expiry_time && (
                        <span className={`text-xs font-medium ${formatExpiryDate(site.expiry_time).startsWith('Expired') ? 'text-red-500' : 'text-gray-500'}`}>
                          {formatExpiryDate(site.expiry_time)}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full w-fit">
                      Permanent
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {/* Desktop Table Footer */}
        <div className="p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg">
          {sites.length} Sites
        </div>
      </div>

      {/* Mobile Card Layout */}
      <div className="flex-1 space-y-4 md:hidden">
        {(search.trim() ? sites.filter(site => site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site) => (
          <div key={site.id} className="p-4 bg-white rounded-lg shadow-md">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center flex-1 min-w-0">
                <h3 className="text-lg font-medium text-gray-900 truncate">{site.site_name}</h3>
                <a href="#" className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
              {/* Enhanced expiry badge for mobile */}
              <div className="flex flex-col items-end flex-shrink-0 ml-3 space-y-1">
                {site.expiry_status === 'Temporary' ? (
                  <>
                    <span
                      className="inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full"
                      title={getTooltipText(site)}
                    >
                      Temporary
                    </span>
                    {site.expiry_time && (
                      <span className={`text-xs font-medium whitespace-nowrap ${formatExpiryDate(site.expiry_time).startsWith('Expired') ? 'text-red-500' : 'text-gray-500'}`}>
                        {formatExpiryDate(site.expiry_time)}
                      </span>
                    )}
                  </>
                ) : (
                  <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">
                    Permanent
                  </span>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center justify-start pt-3 space-x-4 border-t border-gray-100">
              {/* Map Domain Button */}
              <button
                className="flex items-center px-3 py-2 space-x-2 text-blue-500 transition-colors rounded-md hover:text-blue-700 hover:bg-blue-50"
                onClick={() => { setSelectedSiteName(site.site_name); setIsMapDomainOpen(true); }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Map Domain</span>
              </button>

              {/* Go Live Button - only for Temporary sites */}
              {site.expiry_status === 'Temporary' && (
                <button
                  className="flex items-center px-3 py-2 space-x-2 text-green-500 transition-colors rounded-md hover:text-green-700 hover:bg-green-50"
                  onClick={() => { window.location.href = `/payments?siteId=${site.id}` }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span className="text-sm font-medium">Go Live</span>
                </button>
              )}

              {/* Change Plan Button - only for Permanent sites */}
              {site.expiry_status === 'Permanent' && (
                <button
                  className="flex items-center px-3 py-2 space-x-2 text-purple-500 transition-colors rounded-md hover:text-purple-700 hover:bg-purple-50"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <rect x="2" y="7" width="20" height="10" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none" />
                    <path d="M2 11h20" stroke="currentColor" strokeWidth="2" />
                    <circle cx="7" cy="15" r="1" fill="currentColor" />
                    <circle cx="11" cy="15" r="1" fill="currentColor" />
                  </svg>
                  <span className="text-sm font-medium">Change Plan</span>
                </button>
              )}
            </div>
          </div>
        ))}

        {/* Mobile Footer */}
        <div className="p-4 text-sm text-center text-gray-600 bg-white rounded-lg shadow-md">
          {sites.length} Sites
        </div>
      </div>
      <MapDomainModal 
        isOpen={isMapDomainOpen} 
        onClose={() => setIsMapDomainOpen(false)} 
        siteName={selectedSiteName} 
      />

      <AskDomainModal
        isOpen={isAskDomainOpen}
        onYes={() => {
          // open map domain modal
          const site = sites.find(s => s.id === selectedSiteId);
          if (site) {
            setSelectedSiteName(site.site_name);
            setIsMapDomainOpen(true);
          }
          setIsAskDomainOpen(false);
        }}
        onNo={() => {
          router.push(`/dashboard/domain`);
          setIsAskDomainOpen(false);
        }}
      />
    </div>
  );
};

export default DashboardPage;