"use client"

import React, { useState } from 'react';
import { Edit3, Save, X, User, Mail, Phone, MapPin, Calendar, Shield, Bell, Lock } from 'lucide-react';
import { useAuth } from '../../../components/providers/AuthProvider';

export default function ProfilePage() {
  const { user, session, loading: authLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+1.**********',
    address1: '123 Main St.',
    city: 'Los Angeles',
    stateProvince: 'CA',
    postalCode: '90001',
    country: 'US',
    joinDate: 'March 2022',
    avatar: null
  });

  const [settings, setSettings] = useState({
    emailNotifications: true,
    pushNotifications: false,
    twoFactorAuth: false
  });

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to your backend
  };

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSettingChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  if (authLoading || !user) {
    return <div className="flex items-center justify-center min-h-screen">Checking authentication...</div>;
  }

  // Determine if user is OAuth
  const isOAuthUser = user.identities && user.identities.length > 0 && user.identities[0].provider !== 'email';

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header - Responsive */}
      <div className="border-b bg-white/80 backdrop-blur-sm border-slate-200/50">
        <div className="max-w-4xl px-4 py-4 mx-auto sm:px-6">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-bold text-transparent sm:text-2xl bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text">
                Profile Settings
              </h1>
              <p className="mt-1 text-sm sm:text-base text-slate-600">Manage your account and preferences</p>
              <div className="mt-2 text-xs text-gray-700 sm:text-sm">
                <span className="font-semibold break-all">{user.email}</span>
              </div>
            </div>
            <div className="flex items-center flex-shrink-0 gap-2 sm:gap-3">
              {isEditing ? (
                <>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="flex items-center gap-1 px-3 py-2 text-sm transition-all duration-200 rounded-lg sm:gap-2 sm:px-4 text-slate-600 hover:text-slate-800 hover:bg-slate-100 sm:text-base"
                  >
                    <X size={14} className="sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">Cancel</span>
                    <span className="sm:hidden">✕</span>
                  </button>
                  <button
                    onClick={handleSave}
                    className="flex items-center gap-1 px-3 py-2 text-sm text-white transition-all duration-200 rounded-lg shadow-lg sm:gap-2 sm:px-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-blue-500/25 sm:text-base"
                  >
                    <Save size={14} className="sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">Save Changes</span>
                    <span className="sm:hidden">Save</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-1 px-3 py-2 text-sm text-white transition-all duration-200 rounded-lg shadow-lg sm:gap-2 sm:px-4 bg-gradient-to-r from-slate-800 to-slate-600 hover:from-slate-900 hover:to-slate-700 shadow-slate-500/25 sm:text-base"
                >
                  <Edit3 size={14} className="sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Edit Profile</span>
                  <span className="sm:hidden">Edit</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl px-4 py-6 mx-auto sm:px-6 sm:py-8">
        <div className="grid grid-cols-1 gap-6 sm:gap-8">
          {/* Main Content */}
          <div>
            <div className="overflow-hidden border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50">

              {/* Personal Information Section */}
              <div className="p-4 border-b sm:p-6 lg:p-8 border-slate-200/50">
                <div className="mb-6 sm:mb-8">
                  <h3 className="mb-2 text-lg font-semibold sm:text-xl text-slate-800">Personal Information</h3>
                  <p className="text-sm sm:text-base text-slate-600">Update your personal details and profile information</p>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2">
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">First Name</label>
                      <div className="relative">
                        <User className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Last Name</label>
                      <div className="relative">
                        <User className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block mb-2 text-sm font-medium text-slate-700">Address</label>
                      <div className="relative">
                        <MapPin className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.address1}
                          onChange={(e) => handleInputChange('address1', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">City</label>
                      <div className="relative">
                        <MapPin className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">State/Province</label>
                      <div className="relative">
                        <MapPin className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.stateProvince}
                          onChange={(e) => handleInputChange('stateProvince', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Postal Code</label>
                      <div className="relative">
                        <MapPin className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.postalCode}
                          onChange={(e) => handleInputChange('postalCode', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Country</label>
                      <div className="relative">
                        <MapPin className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.country}
                          onChange={(e) => handleInputChange('country', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block mb-2 text-sm font-medium text-slate-700">Email Address</label>
                      <div className="relative">
                        <Mail className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="email"
                          value={profileData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block mb-2 text-sm font-medium text-slate-700">Phone Number</label>
                      <div className="relative">
                        <Phone className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                        <input
                          type="tel"
                          value={profileData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 p-3 text-xs sm:p-4 sm:text-sm text-slate-600 bg-slate-50/50 rounded-xl">
                    <Calendar size={14} className="flex-shrink-0 sm:w-4 sm:h-4" />
                    <span>Member since {profileData.joinDate}</span>
                  </div>
                </div>
              </div>

              {/* Security Settings Section */}
              <div className="p-4 border-b sm:p-6 lg:p-8 border-slate-200/50">
                <div className="mb-6 sm:mb-8">
                  <h3 className="mb-2 text-lg font-semibold sm:text-xl text-slate-800">Security Settings</h3>
                  <p className="text-sm sm:text-base text-slate-600">Manage your account security and authentication</p>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="p-4 border border-blue-200 sm:p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                    <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-slate-800 sm:text-base">Two-Factor Authentication</h4>
                        <p className="mt-1 text-xs sm:text-sm text-slate-600">Add an extra layer of security to your account</p>
                      </div>
                      <div className="flex items-center justify-end flex-shrink-0 sm:justify-start">
                        <input
                          type="checkbox"
                          checked={settings.twoFactorAuth}
                          onChange={(e) => handleSettingChange('twoFactorAuth', e.target.checked)}
                          className="sr-only"
                        />
                        <button
                          onClick={() => handleSettingChange('twoFactorAuth', !settings.twoFactorAuth)}
                          className={`relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full transition-colors ${
                            settings.twoFactorAuth ? 'bg-blue-600' : 'bg-slate-300'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 sm:h-4 sm:w-4 transform rounded-full bg-white transition-transform ${
                              settings.twoFactorAuth ? 'translate-x-5 sm:translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Only show Change Password if not OAuth user */}
                  {!isOAuthUser && (
                    <div className="p-4 border sm:p-6 bg-white/50 rounded-xl border-slate-200">
                      <h4 className="mb-4 text-sm font-semibold text-slate-800 sm:text-base">Change Password</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block mb-2 text-sm font-medium text-slate-700">Current Password</label>
                          <div className="relative">
                            <Lock className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                            <input
                              type="password"
                              className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter current password"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block mb-2 text-sm font-medium text-slate-700">New Password</label>
                          <div className="relative">
                            <Lock className="absolute w-4 h-4 sm:w-5 sm:h-5 left-3 top-3 sm:top-3.5 text-slate-400" />
                            <input
                              type="password"
                              className="w-full py-2.5 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter new password"
                            />
                          </div>
                        </div>
                        <button className="w-full px-4 py-2 text-sm text-white transition-all duration-200 rounded-lg sm:w-auto sm:text-base bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                          Update Password
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Notification Settings Section */}
              <div className="p-4 border-b sm:p-6 lg:p-8 border-slate-200/50">
                <div className="mb-6 sm:mb-8">
                  <h3 className="mb-2 text-lg font-semibold sm:text-xl text-slate-800">Notification Preferences</h3>
                  <p className="text-sm sm:text-base text-slate-600">Choose how you want to be notified</p>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="p-4 border sm:p-6 bg-white/50 rounded-xl border-slate-200">
                    <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-slate-800 sm:text-base">Email Notifications</h4>
                        <p className="mt-1 text-xs sm:text-sm text-slate-600">Receive notifications via email</p>
                      </div>
                      <button
                        onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}
                        className={`relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full transition-colors self-end sm:self-auto ${
                          settings.emailNotifications ? 'bg-blue-600' : 'bg-slate-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-3 w-3 sm:h-4 sm:w-4 transform rounded-full bg-white transition-transform ${
                            settings.emailNotifications ? 'translate-x-5 sm:translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>

                  <div className="p-4 border sm:p-6 bg-white/50 rounded-xl border-slate-200">
                    <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-slate-800 sm:text-base">Push Notifications</h4>
                        <p className="mt-1 text-xs sm:text-sm text-slate-600">Receive push notifications on your device</p>
                      </div>
                      <button
                        onClick={() => handleSettingChange('pushNotifications', !settings.pushNotifications)}
                        className={`relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full transition-colors self-end sm:self-auto ${
                          settings.pushNotifications ? 'bg-blue-600' : 'bg-slate-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-3 w-3 sm:h-4 sm:w-4 transform rounded-full bg-white transition-transform ${
                            settings.pushNotifications ? 'translate-x-5 sm:translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Management Section */}
              <div className="p-4 sm:p-6 lg:p-8">
                <div className="mb-6 sm:mb-8">
                  <h3 className="mb-2 text-lg font-semibold sm:text-xl text-slate-800">Account Management</h3>
                  <p className="text-sm sm:text-base text-slate-600">Manage your account settings</p>
                </div>

                <div className="p-4 border border-red-200 sm:p-6 bg-red-50 rounded-xl">
                  <h4 className="mb-2 text-sm font-semibold text-red-800 sm:text-base">Delete Account</h4>
                  <p className="mb-4 text-xs text-red-600 sm:text-sm">Permanently delete your account and all associated data</p>
                  <button className="w-full px-4 py-2 text-sm text-white transition-colors bg-red-600 rounded-lg sm:w-auto sm:text-base hover:bg-red-700">
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}