/**
 * WordPress Authentication Utilities
 * Handles authentication for WordPress REST API operations
 */

export interface WordPressCredentials {
  username: string;
  password: string;
  baseUrl: string;
}

export interface WordPressAuthResponse {
  success: boolean;
  token?: string;
  error?: string;
}

/**
 * Generate Basic Auth header for WordPress REST API
 */
export const getBasicAuthHeader = (credentials: WordPressCredentials): string => {
  // Remove spaces from application password and encode properly
  const cleanPassword = credentials.password.replace(/\s+/g, '');
  const authString = `${credentials.username}:${cleanPassword}`;
  return `Basic ${btoa(authString)}`;
};

/**
 * Get authentication headers for WordPress API requests
 */
export const getWordPressAuthHeaders = (credentials: WordPressCredentials) => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': getBasicAuthHeader(credentials),
  };
};

/**
 * Validate WordPress credentials by testing authentication
 */
export const validateWordPressCredentials = async (credentials: WordPressCredentials): Promise<WordPressAuthResponse> => {
  try {
    const response = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/users/me`, {
      method: 'GET',
      headers: getWordPressAuthHeaders(credentials),
    });

    if (response.ok) {
      const userData = await response.json();
      return {
        success: true,
        token: getBasicAuthHeader(credentials),
      };
    } else {
      return {
        success: false,
        error: `Authentication failed: ${response.status} ${response.statusText}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

/**
 * Default WordPress credentials using Application Password
 */
export const DEFAULT_WORDPRESS_CREDENTIALS: WordPressCredentials = {
  username: 'kanaxiwufo0003',
  password: 'm2GY w2QW aFok jvwD 1XOf fmb0',
  baseUrl: 'https://new-hestia-project3.instawp.xyz',
}; 