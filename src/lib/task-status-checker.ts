/**
 * Task Status Checker Utility
 * Handles polling of InstaWP task status with retry logic
 */

interface TaskStatusResponse {
  success: boolean;
  task_status: 'in progress' | 'completed' | 'failed';
  message: string;
  data: any;
}

interface TaskStatusCheckerOptions {
  maxRetries?: number;
  retryDelay?: number; // in milliseconds
  timeout?: number; // in milliseconds
  baseUrl?: string; // base URL for API requests
}

const DEFAULT_OPTIONS: Required<Omit<TaskStatusCheckerOptions, 'baseUrl'>> = {
  maxRetries: 30, // 30 retries
  retryDelay: 2000, // 2 seconds between retries
  timeout: 60000, // 60 seconds total timeout
};

/**
 * Get the appropriate base URL for API requests
 */
function getBaseUrl(options?: { baseUrl?: string }): string {
  // If baseUrl is explicitly provided, use it
  if (options?.baseUrl) {
    return options.baseUrl;
  }

  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Check environment variables
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL;
  }

  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Default fallback
  return 'http://localhost:3000';
}

/**
 * Poll task status until completion or failure
 */
export async function pollTaskStatus(
  taskId: string,
  instawpToken: string,
  options: TaskStatusCheckerOptions = {}
): Promise<TaskStatusResponse> {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const startTime = Date.now();
  let retryCount = 0;

  // Get the base URL for API requests
  const baseUrl = getBaseUrl(options);

  while (retryCount < config.maxRetries) {
    try {
      // Check if we've exceeded the timeout
      if (Date.now() - startTime > config.timeout) {
        throw new Error(`Task status check timed out after ${config.timeout}ms`);
      }

      // Make the status check request with absolute URL
      const apiUrl = `${baseUrl}/api/check-task-status?task_id=${taskId}&token=${instawpToken}`;
      console.log(`🔍 Checking task status: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
      }

      const result: TaskStatusResponse = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Task status check failed');
      }

      // If task is completed, return the result
      if (result.task_status === 'completed') {
        console.log(`✅ Task ${taskId} completed successfully`);
        return result;
      }

      // If task failed, throw an error
      if (result.task_status === 'failed') {
        throw new Error(`Task ${taskId} failed: ${result.message}`);
      }

      // Task is still in progress, wait and retry
      console.log(`⏳ Task ${taskId} still in progress (attempt ${retryCount + 1}/${config.maxRetries})`);
      
      if (retryCount < config.maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      }

      retryCount++;
    } catch (error) {
      console.error(`Error checking task status (attempt ${retryCount + 1}):`, error);
      
      // If this is the last retry, throw the error
      if (retryCount >= config.maxRetries - 1) {
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      retryCount++;
    }
  }

  throw new Error(`Task status check exceeded maximum retries (${config.maxRetries})`);
}

/**
 * Check task status once without polling
 */
export async function checkTaskStatusOnce(
  taskId: string,
  instawpToken: string,
  baseUrl?: string
): Promise<TaskStatusResponse> {
  // Get the base URL for API requests
  const apiBaseUrl = getBaseUrl({ baseUrl });

  const apiUrl = `${apiBaseUrl}/api/check-task-status?task_id=${taskId}&token=${instawpToken}`;
  const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
  }

  const result: TaskStatusResponse = await response.json();

  if (!result.success) {
    throw new Error(result.message || 'Task status check failed');
  }

  return result;
} 