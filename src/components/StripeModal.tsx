import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

interface StripeModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: {
    name: string;
    price: number | string;
    yearlyPrice: number | string;
    description: string;
    features: string[];
    id: string;
  } | null;
  isYearly: boolean;
  publishableKey: string;
  onPaymentSuccess?: (result: any) => void;
}

const stripePromise = (publishableKey: string) => loadStripe(publishableKey);

const CheckoutForm: React.FC<{ plan: any; isYearly: boolean; onPaymentSuccess?: (result: any) => void }> = ({ plan, isYearly, onPaymentSuccess }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);
  const [clientSecret, setClientSecret] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!plan) return;
    setClientSecret(null);
    setError(null);
    setSuccess(false);
    // Fetch PaymentIntent client secret from backend
    const fetchClientSecret = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ planId: plan.id, isYearly }),
        });
        const data = await res.json();
        if (data.clientSecret) {
          setClientSecret(data.clientSecret);
        } else {
          setError(data.error || 'Failed to initialize payment.');
        }
      } catch (err) {
        setError('Failed to initialize payment.');
      } finally {
        setLoading(false);
      }
    };
    fetchClientSecret();
  }, [plan, isYearly]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    if (!stripe || !elements || !clientSecret) {
      setError('Stripe is not ready.');
      setLoading(false);
      return;
    }
    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError('Card element not found.');
      setLoading(false);
      return;
    }
    const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: cardElement,
      },
    });
    if (stripeError) {
      setError(stripeError.message || 'Payment failed.');
      setLoading(false);
      return;
    }
    if (paymentIntent && paymentIntent.status === 'succeeded') {
      setSuccess(true);
      if (onPaymentSuccess) onPaymentSuccess({ plan, isYearly, paymentIntent });
    } else {
      setError('Payment was not successful.');
    }
    setLoading(false);
  };

  if (success) {
    return <div className="text-green-600 font-semibold text-center">Payment successful! Thank you.</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="mb-2">
        <CardElement options={{ style: { base: { fontSize: '16px' } } }} />
      </div>
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <button
        type="submit"
        disabled={!stripe || loading || !clientSecret}
        className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 transition flex items-center justify-center"
      >
        {loading ? (
          <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
        ) : null}
        {loading ? 'Processing...' : `Pay $${isYearly ? plan.yearlyPrice : plan.price} AUD`}
      </button>
    </form>
  );
};

const StripeModal: React.FC<StripeModalProps> = ({ isOpen, onClose, plan, isYearly, publishableKey, onPaymentSuccess }) => {
  if (!isOpen || !plan) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
      <div className="w-full max-w-lg p-6 bg-white rounded-lg shadow-xl relative">
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h2 className="text-2xl font-bold mb-2 text-black">{plan.name} Plan</h2>
        <div className="mb-2 text-lg text-gray-700">
          {isYearly ? plan.yearlyPrice : plan.price} <span className="text-sm">AUD /mo</span>
        </div>
        <div className="mb-4 text-gray-600">{plan.description}</div>
        <ul className="mb-4 space-y-1 text-sm text-gray-700">
          {plan.features.map((f: string, i: number) => <li key={i}>• {f}</li>)}
        </ul>
        <Elements stripe={stripePromise(publishableKey)}>
          <CheckoutForm plan={plan} isYearly={isYearly} onPaymentSuccess={onPaymentSuccess} />
        </Elements>
      </div>
    </div>
  );
};

export default StripeModal; 